import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the actual.json file
const actualDataPath = path.join(__dirname, '../src/data/actual.json');
const actualData = JSON.parse(fs.readFileSync(actualDataPath, 'utf8'));

console.log('🔍 Analyzing all categories in actual.json...\n');

// Function to parse questions from all frameworks
const parseAllQuestions = () => {
  const parsedQuestions = {};
  const categoryStats = {};
  
  actualData.forEach((framework) => {
    console.log(`📋 Framework: ${framework.name}`);
    
    if (framework.supplySections) {
      framework.supplySections.forEach((section) => {
        const sectionName = section.name;
        console.log(`  📂 Section: ${sectionName}`);
        
        if (section.supplyChecklists) {
          section.supplyChecklists.forEach((checklist) => {
            const subcategoryName = checklist.name;
            console.log(`    📄 Checklist: ${subcategoryName}`);
            
            try {
              const parsedValues = JSON.parse(checklist.values);
              const components = parsedValues.components || [];
              
              components.forEach((component) => {
                const questions = component.questions || [];
                
                questions.forEach((q) => {
                  if (q.questionNumber) {
                    // Use framework name as the main category
                    const mainCategory = framework.name;
                    const subCategory = sectionName;
                    const subSubCategory = subcategoryName;
                    
                    parsedQuestions[q.questionNumber] = {
                      question: q.text,
                      response: generateMockResponse(q),
                      framework: mainCategory,
                      category: subCategory,
                      subcategory: subSubCategory
                    };
                    
                    // Track statistics
                    if (!categoryStats[mainCategory]) {
                      categoryStats[mainCategory] = {};
                    }
                    if (!categoryStats[mainCategory][subCategory]) {
                      categoryStats[mainCategory][subCategory] = 0;
                    }
                    categoryStats[mainCategory][subCategory]++;
                    
                    console.log(`      ✅ ${q.questionNumber}: ${q.text.substring(0, 60)}...`);
                  }
                });
              });
            } catch (error) {
              console.warn(`      ❌ Failed to parse values for checklist ${subcategoryName}:`, error.message);
            }
          });
        }
      });
    }
    console.log('');
  });
  
  return { parsedQuestions, categoryStats };
};

// Function to generate mock responses based on question type
const generateMockResponse = (question) => {
  const responses = {
    A: ["Yes", "No", "N/A"],
    B: ["Yes", "No"],
    C: ["Excellent", "Good", "Fair", "Poor"],
    D: ["Municipal Water", "Ground Water", "Surface Water", "Recycled Water"]
  };
  
  const typeResponses = responses[question.type] || ["Yes", "No"];
  return typeResponses[Math.floor(Math.random() * typeResponses.length)];
};

// Parse all questions
const { parsedQuestions, categoryStats } = parseAllQuestions();

console.log(`\n📊 SUMMARY:`);
console.log(`Total questions found: ${Object.keys(parsedQuestions).length}`);

console.log(`\n📈 Questions by Framework and Category:`);
Object.entries(categoryStats).forEach(([framework, categories]) => {
  const totalFrameworkQuestions = Object.values(categories).reduce((sum, count) => sum + count, 0);
  console.log(`\n🏢 ${framework} (${totalFrameworkQuestions} questions):`);
  
  Object.entries(categories).forEach(([category, count]) => {
    console.log(`  📂 ${category}: ${count} questions`);
  });
});

console.log(`\n🎯 All frameworks and categories are now ready to be linked to the heatmap!`);
