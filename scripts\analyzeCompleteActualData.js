import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the actual.json file
const actualDataPath = path.join(__dirname, '../src/data/actual.json');
const actualData = JSON.parse(fs.readFileSync(actualDataPath, 'utf8'));

console.log('🔍 Analyzing COMPLETE actual.json structure...\n');

// Function to analyze all data in actual.json
const analyzeCompleteData = () => {
  const allData = {
    frameworks: {},
    metadata: {},
    totalQuestions: 0,
    totalComponents: 0,
    totalChecklists: 0,
    additionalFields: new Set()
  };
  
  actualData.forEach((framework, frameworkIndex) => {
    console.log(`\n📋 Framework ${frameworkIndex + 1}: ${framework.name}`);
    console.log(`   ID: ${framework.id}`);
    console.log(`   Created: ${framework.createdAt}`);
    console.log(`   Updated: ${framework.updatedAt}`);
    console.log(`   Status: ${framework.status}`);
    console.log(`   Remarks: ${framework.remarks}`);
    
    // Track framework metadata
    allData.frameworks[framework.name] = {
      id: framework.id,
      createdAt: framework.createdAt,
      updatedAt: framework.updatedAt,
      status: framework.status,
      remarks: framework.remarks,
      sections: {},
      totalQuestions: 0,
      totalChecklists: 0
    };
    
    // Track all field names
    Object.keys(framework).forEach(key => allData.additionalFields.add(`framework.${key}`));
    
    if (framework.supplySections) {
      framework.supplySections.forEach((section, sectionIndex) => {
        console.log(`\n  📂 Section ${sectionIndex + 1}: ${section.name}`);
        console.log(`     ID: ${section.id}`);
        console.log(`     Created: ${section.createdAt}`);
        console.log(`     Updated: ${section.updatedAt}`);
        console.log(`     Status: ${section.status}`);
        console.log(`     Supply Category ID: ${section.supplyCategoryId}`);
        
        // Track section metadata
        allData.frameworks[framework.name].sections[section.name] = {
          id: section.id,
          createdAt: section.createdAt,
          updatedAt: section.updatedAt,
          status: section.status,
          supplyCategoryId: section.supplyCategoryId,
          checklists: {},
          totalQuestions: 0
        };
        
        // Track all field names
        Object.keys(section).forEach(key => allData.additionalFields.add(`section.${key}`));
        
        if (section.supplyChecklists) {
          section.supplyChecklists.forEach((checklist, checklistIndex) => {
            console.log(`\n    📄 Checklist ${checklistIndex + 1}: ${checklist.name}`);
            console.log(`       ID: ${checklist.id}`);
            console.log(`       Created: ${checklist.createdAt}`);
            console.log(`       Updated: ${checklist.updatedAt}`);
            console.log(`       Status: ${checklist.status}`);
            console.log(`       Supply Category ID: ${checklist.supplyCategoryId}`);
            console.log(`       Supply Section ID: ${checklist.supplySectionId}`);
            console.log(`       Created By ID: ${checklist.createdById}`);
            
            allData.totalChecklists++;
            allData.frameworks[framework.name].totalChecklists++;
            
            // Track checklist metadata
            const checklistData = {
              id: checklist.id,
              createdAt: checklist.createdAt,
              updatedAt: checklist.updatedAt,
              status: checklist.status,
              supplyCategoryId: checklist.supplyCategoryId,
              supplySectionId: checklist.supplySectionId,
              createdById: checklist.createdById,
              questions: [],
              components: [],
              metadata: {}
            };
            
            // Track all field names
            Object.keys(checklist).forEach(key => allData.additionalFields.add(`checklist.${key}`));
            
            try {
              const parsedValues = JSON.parse(checklist.values);
              console.log(`       Checklist ID: ${parsedValues.checklistId}`);
              console.log(`       Checklist Name: ${parsedValues.checklistName}`);
              console.log(`       Last Modified: ${parsedValues.lastModified}`);
              console.log(`       Total Components: ${parsedValues.totalComponents}`);
              console.log(`       Total Questions: ${parsedValues.totalQuestions}`);
              
              // Store parsed metadata
              checklistData.metadata = {
                checklistId: parsedValues.checklistId,
                checklistName: parsedValues.checklistName,
                lastModified: parsedValues.lastModified,
                totalComponents: parsedValues.totalComponents,
                totalQuestions: parsedValues.totalQuestions
              };
              
              // Track all field names in parsed values
              Object.keys(parsedValues).forEach(key => allData.additionalFields.add(`values.${key}`));
              
              const components = parsedValues.components || [];
              allData.totalComponents += components.length;
              
              components.forEach((component, componentIndex) => {
                console.log(`\n      🔧 Component ${componentIndex + 1}:`);
                console.log(`         ID: ${component.id}`);
                console.log(`         Type: ${component.type}`);
                console.log(`         Title: ${component.title}`);
                console.log(`         Content: ${component.content}`);
                console.log(`         Description: ${component.description}`);
                console.log(`         Required: ${component.required}`);
                console.log(`         Order: ${component.order}`);
                
                // Track component data
                const componentData = {
                  id: component.id,
                  type: component.type,
                  title: component.title,
                  content: component.content,
                  description: component.description,
                  required: component.required,
                  order: component.order,
                  questions: []
                };
                
                // Track all field names in components
                Object.keys(component).forEach(key => allData.additionalFields.add(`component.${key}`));
                
                const questions = component.questions || [];
                
                questions.forEach((q, questionIndex) => {
                  console.log(`\n        ❓ Question ${questionIndex + 1}:`);
                  console.log(`           ID: ${q.id}`);
                  console.log(`           Question Number: ${q.questionNumber}`);
                  console.log(`           Text: ${q.text.substring(0, 80)}...`);
                  console.log(`           Type: ${q.type}`);
                  console.log(`           Category: ${q.category}`);
                  console.log(`           Required Response: ${q.requiredResponse}`);
                  console.log(`           Yes Score: ${q.yesScore}`);
                  console.log(`           No Score: ${q.noScore}`);
                  console.log(`           NA Score: ${q.naScore}`);
                  console.log(`           Numerator: ${q.numerator}`);
                  console.log(`           Denominator: ${q.denominator}`);
                  console.log(`           Marks: ${q.marks}`);
                  console.log(`           Legal Compliance: ${q.legalCompliance}`);
                  
                  allData.totalQuestions++;
                  allData.frameworks[framework.name].totalQuestions++;
                  allData.frameworks[framework.name].sections[section.name].totalQuestions++;
                  
                  // Track question data
                  const questionData = {
                    id: q.id,
                    questionNumber: q.questionNumber,
                    text: q.text,
                    type: q.type,
                    category: q.category,
                    requiredResponse: q.requiredResponse,
                    yesScore: q.yesScore,
                    noScore: q.noScore,
                    naScore: q.naScore,
                    numerator: q.numerator,
                    denominator: q.denominator,
                    marks: q.marks,
                    legalCompliance: q.legalCompliance
                  };
                  
                  // Track all field names in questions
                  Object.keys(q).forEach(key => allData.additionalFields.add(`question.${key}`));
                  
                  componentData.questions.push(questionData);
                  checklistData.questions.push(questionData);
                });
                
                checklistData.components.push(componentData);
              });
            } catch (error) {
              console.warn(`       ❌ Failed to parse values for checklist ${checklist.name}:`, error.message);
            }
            
            allData.frameworks[framework.name].sections[section.name].checklists[checklist.name] = checklistData;
          });
        }
      });
    }
  });
  
  return allData;
};

// Analyze all data
const completeData = analyzeCompleteData();

console.log(`\n\n📊 COMPLETE DATA SUMMARY:`);
console.log(`🏢 Total Frameworks: ${Object.keys(completeData.frameworks).length}`);
console.log(`📂 Total Sections: ${Object.values(completeData.frameworks).reduce((sum, f) => sum + Object.keys(f.sections).length, 0)}`);
console.log(`📄 Total Checklists: ${completeData.totalChecklists}`);
console.log(`🔧 Total Components: ${completeData.totalComponents}`);
console.log(`❓ Total Questions: ${completeData.totalQuestions}`);

console.log(`\n📋 All Data Fields Found:`);
Array.from(completeData.additionalFields).sort().forEach(field => {
  console.log(`  🔹 ${field}`);
});

console.log(`\n🎯 COMPLETE actual.json analysis finished!`);
console.log(`📈 All ${completeData.totalQuestions} questions and ${completeData.totalComponents} components are ready to be linked!`);

// Save complete analysis to file
const outputPath = path.join(__dirname, '../analysis_complete_actual_data.json');
fs.writeFileSync(outputPath, JSON.stringify(completeData, null, 2));
console.log(`\n💾 Complete analysis saved to: ${outputPath}`);
