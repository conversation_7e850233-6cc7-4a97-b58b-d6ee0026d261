import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the actual.json file
const actualDataPath = path.join(__dirname, '../src/data/actual.json');
const actualData = JSON.parse(fs.readFileSync(actualDataPath, 'utf8'));

// Function to parse questions from the values field
function parseQuestions(valuesString) {
  try {
    const parsedValues = JSON.parse(valuesString);
    return parsedValues.components?.[0]?.questions || [];
  } catch (error) {
    console.error('Error parsing values:', error);
    return [];
  }
}

// Function to generate a mock response based on question type and scoring
function generateMockResponse(question) {
  // For questions with high scores (yesScore), return "Yes"
  // For questions with low scores or noScore, return "No" 
  // For descriptive questions, return "N/A"
  
  if (question.type === 'D') {
    return 'N/A'; // Descriptive questions
  }
  
  if (question.type === 'C') {
    return 'N/A'; // Calculation/percentage questions
  }
  
  // For boolean questions (A, B), use scoring to determine response
  if (question.yesScore && question.yesScore > 0) {
    // If yesScore is higher, more likely to be "Yes"
    return Math.random() > 0.3 ? 'Yes' : 'No';
  } else if (question.noScore && question.noScore > 0) {
    // If noScore is higher, more likely to be "No"
    return Math.random() > 0.7 ? 'Yes' : 'No';
  }
  
  // Default random response
  const responses = ['Yes', 'No', 'N/A'];
  return responses[Math.floor(Math.random() * responses.length)];
}

// Extract all questions from the actual data
const extractedQuestions = {};

// Process the supplier environmental framework
const supplierFramework = actualData[0];
if (supplierFramework && supplierFramework.supplySections) {
  supplierFramework.supplySections.forEach(section => {
    const sectionName = section.name;
    
    if (section.supplyChecklists) {
      section.supplyChecklists.forEach(checklist => {
        const subcategoryName = checklist.name;
        
        if (checklist.values) {
          const questions = parseQuestions(checklist.values);
          
          questions.forEach(question => {
            const questionNumber = question.questionNumber;
            if (questionNumber) {
              console.log(`Found question: ${questionNumber} - ${question.text.substring(0, 50)}...`);
              extractedQuestions[questionNumber] = {
                question: question.text,
                response: generateMockResponse(question),
                category: sectionName,
                subcategory: subcategoryName
              };
            }
          });
        }
      });
    }
  });
}

// Create the output structure
const outputData = {
  questions: extractedQuestions
};

// Write the new questionsData.json file
const outputPath = path.join(__dirname, '../src/data/questionsData.json');
console.log('Writing to:', outputPath);
fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2));

console.log(`Extracted ${Object.keys(extractedQuestions).length} questions from actual.json`);
console.log('Generated new questionsData.json file');

// Log some sample questions for verification
console.log('\nSample extracted questions:');
Object.entries(extractedQuestions).slice(0, 5).forEach(([key, value]) => {
  console.log(`${key}: ${value.question.substring(0, 80)}...`);
});

// Also generate a mapping for the heatmap data
console.log('\nAll question IDs extracted:');
console.log(Object.keys(extractedQuestions).sort());
