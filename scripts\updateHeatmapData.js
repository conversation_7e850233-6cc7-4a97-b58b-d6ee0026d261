import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the current heatmapData.json and questionsData.json
const heatmapDataPath = path.join(__dirname, '../src/data/heatmapData.json');
const questionsDataPath = path.join(__dirname, '../src/data/questionsData.json');

const heatmapData = JSON.parse(fs.readFileSync(heatmapDataPath, 'utf8'));
const questionsData = JSON.parse(fs.readFileSync(questionsDataPath, 'utf8'));

// Get all available question IDs from the new questionsData
const availableQuestionIds = Object.keys(questionsData.questions);

console.log('Available question IDs:', availableQuestionIds.length);

// Function to find matching question IDs for each subcategory
function findMatchingQuestions(subcategoryName, sectionName) {
  const matchingQuestions = [];
  
  // Look for questions that match the category and subcategory
  for (const [questionId, questionData] of Object.entries(questionsData.questions)) {
    if (questionData.category === sectionName && questionData.subcategory === subcategoryName) {
      matchingQuestions.push(questionId);
    }
  }
  
  return matchingQuestions.sort();
}

// Update the heatmap data structure
const updatedHeatmapData = JSON.parse(JSON.stringify(heatmapData)); // Deep clone

updatedHeatmapData.supplierEnvironmentalFramework.categories.forEach(category => {
  category.subcategories.forEach(subcategory => {
    const matchingQuestions = findMatchingQuestions(subcategory.name, category.name);
    
    console.log(`\n${category.name} -> ${subcategory.name}:`);
    console.log(`  Found ${matchingQuestions.length} matching questions:`, matchingQuestions);
    
    if (matchingQuestions.length > 0) {
      // Update the scores array to use the new question IDs
      subcategory.scores = matchingQuestions.map((questionId, index) => {
        // Keep the original score if it exists, otherwise generate a random one
        const originalScore = subcategory.scores[index] || { value: Math.floor(Math.random() * 100), status: 'fair' };
        
        return {
          period: questionId,
          value: originalScore.value,
          status: getStatusFromValue(originalScore.value)
        };
      });
    } else {
      console.log(`  Warning: No matching questions found for ${category.name} -> ${subcategory.name}`);
    }
  });
});

// Function to determine status from value
function getStatusFromValue(value) {
  if (value >= 90) return 'excellent';
  if (value >= 70) return 'good';
  if (value >= 50) return 'fair';
  if (value >= 30) return 'poor';
  return 'critical';
}

// Write the updated heatmap data
const outputPath = path.join(__dirname, '../src/data/heatmapData.json');
fs.writeFileSync(outputPath, JSON.stringify(updatedHeatmapData, null, 2));

console.log('\nUpdated heatmapData.json with new question IDs');
console.log('Total categories processed:', updatedHeatmapData.supplierEnvironmentalFramework.categories.length);

// Summary of changes
let totalQuestions = 0;
updatedHeatmapData.supplierEnvironmentalFramework.categories.forEach(category => {
  category.subcategories.forEach(subcategory => {
    totalQuestions += subcategory.scores.length;
  });
});

console.log(`Total questions mapped in heatmap: ${totalQuestions}`);
