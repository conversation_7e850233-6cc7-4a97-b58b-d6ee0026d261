import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the actual.json file
const actualDataPath = path.join(__dirname, '../src/data/actual.json');
const actualData = JSON.parse(fs.readFileSync(actualDataPath, 'utf8'));

console.log('🔍 Verifying actual.json integration...\n');

// Function to parse questions from actual.json (same as in component)
const parseActualQuestions = () => {
  const parsedQuestions = {};
  
  actualData.forEach((framework) => {
    if (framework.name === "SUPPLIER ENVIRONMENTAL FRAMEWORK") {
      framework.supplySections.forEach((section) => {
        section.supplyChecklists.forEach((checklist) => {
          try {
            const parsedValues = JSON.parse(checklist.values);
            const components = parsedValues.components || [];
            
            components.forEach((component) => {
              const questions = component.questions || [];
              
              questions.forEach((q) => {
                if (q.questionNumber) {
                  parsedQuestions[q.questionNumber] = {
                    question: q.text,
                    response: generateMockResponse(q),
                    category: section.name,
                    subcategory: checklist.name
                  };
                }
              });
            });
          } catch (error) {
            console.warn(`Failed to parse values for checklist ${checklist.name}:`, error);
          }
        });
      });
    }
  });
  
  return parsedQuestions;
};

// Function to generate mock responses based on question type
const generateMockResponse = (question) => {
  const responses = {
    A: ["Yes", "No", "N/A"],
    B: ["Yes", "No"],
    C: ["Excellent", "Good", "Fair", "Poor"],
    D: ["Municipal Water", "Ground Water", "Surface Water", "Recycled Water"]
  };
  
  const typeResponses = responses[question.type] || ["Yes", "No"];
  return typeResponses[Math.floor(Math.random() * typeResponses.length)];
};

// Parse questions
const questions = parseActualQuestions();

console.log(`✅ Successfully parsed ${Object.keys(questions).length} questions from actual.json`);
console.log('\n📋 Sample questions:');

// Show first 5 questions
Object.entries(questions).slice(0, 5).forEach(([id, question]) => {
  console.log(`  ${id}: ${question.question.substring(0, 80)}...`);
  console.log(`    Category: ${question.category} | Subcategory: ${question.subcategory}`);
  console.log(`    Response: ${question.response}\n`);
});

// Show categories breakdown
const categoryCounts = {};
Object.values(questions).forEach(q => {
  categoryCounts[q.category] = (categoryCounts[q.category] || 0) + 1;
});

console.log('📊 Questions by category:');
Object.entries(categoryCounts).forEach(([category, count]) => {
  console.log(`  ${category}: ${count} questions`);
});

console.log('\n🎯 Integration verification complete!');
console.log('The actual.json file is now linked to the heatmap component.');
