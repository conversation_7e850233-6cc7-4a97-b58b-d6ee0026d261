import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the actual.json file
const actualDataPath = path.join(__dirname, '../src/data/actual.json');
const actualData = JSON.parse(fs.readFileSync(actualDataPath, 'utf8'));

console.log('🔍 Verifying ALL categories integration from actual.json...\n');

// Function to map framework names to shorter category names for UI (same as in component)
const mapFrameworkToCategory = (frameworkName) => {
  const frameworkMap = {
    "SUPPLIER SOCIAL STEWARDSHIP FRAMEWORK": "Social Stewardship",
    "SUPPLIER HEALTH & SAFETY FRAMEWORK": "Health & Safety", 
    "LEGAL COMPLIANCES": "Legal Compliance",
    "SUPPLIER GOVERNANCE FRAMEWORK": "Governance",
    "SUPPLIER SUSTAINABILITY AMBASSADORSHIP FRAMEWORK": "Sustainability"
  };
  
  return frameworkMap[frameworkName] || frameworkName;
};

// Function to generate mock responses based on question type
const generateMockResponse = (question) => {
  const responses = {
    A: ["Yes", "No", "N/A"],
    B: ["Yes", "No"],
    C: ["Excellent", "Good", "Fair", "Poor"],
    D: ["Municipal Water", "Ground Water", "Surface Water", "Recycled Water"]
  };
  
  const typeResponses = responses[question.type] || ["Yes", "No"];
  return typeResponses[Math.floor(Math.random() * typeResponses.length)];
};

// Function to parse questions from actual.json - ALL FRAMEWORKS (same as in component)
const parseActualQuestions = () => {
  const parsedQuestions = {};
  
  actualData.forEach((framework) => {
    // Process ALL frameworks, not just environmental
    const frameworkName = framework.name;
    
    if (framework.supplySections) {
      framework.supplySections.forEach((section) => {
        const sectionName = section.name;
        
        if (section.supplyChecklists) {
          section.supplyChecklists.forEach((checklist) => {
            const checklistName = checklist.name;
            
            try {
              const parsedValues = JSON.parse(checklist.values);
              const components = parsedValues.components || [];
              
              components.forEach((component) => {
                const questions = component.questions || [];
                
                questions.forEach((q) => {
                  if (q.questionNumber) {
                    // Map framework names to shorter category names for UI
                    let categoryName = sectionName;
                    
                    // For non-environmental frameworks, use framework name as category
                    if (frameworkName !== "SUPPLIER ENVIRONMENTAL FRAMEWORK") {
                      categoryName = mapFrameworkToCategory(frameworkName);
                    }
                    
                    parsedQuestions[q.questionNumber] = {
                      question: q.text,
                      response: generateMockResponse(q),
                      category: categoryName,
                      subcategory: checklistName
                    };
                  }
                });
              });
            } catch (error) {
              console.warn(`Failed to parse values for checklist ${checklistName}:`, error);
            }
          });
        }
      });
    }
  });
  
  return parsedQuestions;
};

// Parse questions
const questions = parseActualQuestions();

console.log(`✅ Successfully parsed ${Object.keys(questions).length} questions from actual.json`);

// Show categories breakdown
const categoryCounts = {};
Object.values(questions).forEach(q => {
  categoryCounts[q.category] = (categoryCounts[q.category] || 0) + 1;
});

console.log('\n📊 Questions by Category:');
Object.entries(categoryCounts)
  .sort(([,a], [,b]) => b - a)
  .forEach(([category, count]) => {
    console.log(`  📂 ${category}: ${count} questions`);
  });

console.log('\n📋 Sample questions from each category:');

// Show sample questions from each category
Object.entries(categoryCounts).forEach(([category, count]) => {
  const categoryQuestions = Object.entries(questions).filter(([id, q]) => q.category === category);
  const sampleQuestion = categoryQuestions[0];
  
  if (sampleQuestion) {
    const [id, question] = sampleQuestion;
    console.log(`\n📂 ${category} (${count} questions):`);
    console.log(`  🔹 ${id}: ${question.question.substring(0, 80)}...`);
    console.log(`    Subcategory: ${question.subcategory}`);
    console.log(`    Response: ${question.response}`);
  }
});

console.log(`\n🎯 ALL ${Object.keys(questions).length} questions from ALL frameworks are now linked to the heatmap!`);
console.log(`📈 Total categories: ${Object.keys(categoryCounts).length}`);
console.log(`🔗 Categories: ${Object.keys(categoryCounts).join(', ')}`);
