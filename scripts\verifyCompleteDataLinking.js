import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the actual.json file
const actualDataPath = path.join(__dirname, '../src/data/actual.json');
const actualData = JSON.parse(fs.readFileSync(actualDataPath, 'utf8'));

console.log('🔗 Verifying COMPLETE data linking from actual.json to heatmap...\n');

// Simulate the exact parsing logic from EnvironmentalHeatmap.tsx
const mapFrameworkToCategory = (frameworkName) => {
  const frameworkMap = {
    "SUPPLIER SOCIAL STEWARDSHIP FRAMEWORK": "Social Stewardship",
    "SUPPLIER HEALTH & SAFETY FRAMEWORK": "Health & Safety", 
    "LEGAL COMPLIANCES": "Legal Compliance",
    "SUPPLIER GOVERNANCE FRAMEWORK": "Governance",
    "SUPPLIER SUSTAINABILITY AMBASSADORSHIP FRAMEWORK": "Sustainability"
  };
  return frameworkMap[frameworkName] || frameworkName;
};

const generateMockResponse = (question) => {
  const responses = {
    A: ["Yes", "No", "N/A"],
    B: ["Yes", "No"],
    C: ["Excellent", "Good", "Fair", "Poor"],
    D: ["Municipal Water", "Ground Water", "Surface Water", "Recycled Water"]
  };
  
  const responseOptions = responses[question.type] || ["Yes", "No"];
  return responseOptions[Math.floor(Math.random() * responseOptions.length)];
};

const parseActualQuestions = () => {
  const parsedQuestions = {};
  const allMetadata = {
    totalFrameworks: 0,
    totalSections: 0,
    totalChecklists: 0,
    totalComponents: 0,
    totalQuestions: 0,
    metadataFields: new Set(),
    categoriesFound: new Set(),
    questionTypes: new Set(),
    requiredResponseTypes: new Set(),
    legalComplianceCount: 0,
    scoringQuestions: 0
  };
  
  actualData.forEach((framework) => {
    allMetadata.totalFrameworks++;
    const frameworkName = framework.name;
    
    if (framework.supplySections) {
      framework.supplySections.forEach((section) => {
        allMetadata.totalSections++;
        const sectionName = section.name;
        
        if (section.supplyChecklists) {
          section.supplyChecklists.forEach((checklist) => {
            allMetadata.totalChecklists++;
            const checklistName = checklist.name;
            
            try {
              const parsedValues = JSON.parse(checklist.values);
              const components = parsedValues.components || [];
              
              components.forEach((component) => {
                allMetadata.totalComponents++;
                const questions = component.questions || [];
                
                questions.forEach((q) => {
                  if (q.questionNumber) {
                    allMetadata.totalQuestions++;
                    
                    // Track question types and metadata
                    allMetadata.questionTypes.add(q.type);
                    if (q.requiredResponse) allMetadata.requiredResponseTypes.add(q.requiredResponse);
                    if (q.legalCompliance) allMetadata.legalComplianceCount++;
                    if (q.yesScore || q.noScore || q.naScore) allMetadata.scoringQuestions++;
                    
                    // Map framework names to shorter category names for UI
                    let categoryName = sectionName;
                    
                    // For non-environmental frameworks, use framework name as category
                    if (frameworkName !== "SUPPLIER ENVIRONMENTAL FRAMEWORK") {
                      categoryName = mapFrameworkToCategory(frameworkName);
                    }
                    
                    allMetadata.categoriesFound.add(categoryName);
                    
                    // Create the complete question object with ALL metadata
                    const questionWithMetadata = {
                      question: q.text,
                      response: generateMockResponse(q),
                      category: categoryName,
                      subcategory: checklistName,
                      // Capture ALL metadata from actual.json
                      metadata: {
                        // Question metadata
                        id: q.id,
                        type: q.type,
                        questionNumber: q.questionNumber,
                        numerator: q.numerator,
                        denominator: q.denominator,
                        yesScore: q.yesScore,
                        noScore: q.noScore,
                        naScore: q.naScore,
                        requiredResponse: q.requiredResponse,
                        legalCompliance: q.legalCompliance,
                        marks: q.marks,
                        // Component metadata
                        componentId: component.id,
                        componentType: component.type,
                        componentTitle: component.title,
                        componentDescription: component.description,
                        componentRequired: component.required,
                        componentOrder: component.order,
                        // Checklist metadata
                        checklistId: checklist.id,
                        checklistName: checklist.name,
                        checklistCreatedAt: checklist.createdAt,
                        checklistUpdatedAt: checklist.updatedAt,
                        checklistStatus: checklist.status,
                        checklistLastModified: parsedValues.lastModified,
                        checklistTotalComponents: parsedValues.totalComponents,
                        checklistTotalQuestions: parsedValues.totalQuestions,
                        // Section metadata
                        sectionId: section.id,
                        sectionName: section.name,
                        sectionCreatedAt: section.createdAt,
                        sectionUpdatedAt: section.updatedAt,
                        sectionStatus: section.status,
                        sectionSupplyCategoryId: section.supplyCategoryId,
                        // Framework metadata
                        frameworkId: framework.id,
                        frameworkName: framework.name,
                        frameworkCreatedAt: framework.createdAt,
                        frameworkUpdatedAt: framework.updatedAt,
                        frameworkStatus: framework.status,
                        frameworkRemarks: framework.remarks
                      }
                    };
                    
                    // Track all metadata fields that are being captured
                    Object.keys(questionWithMetadata.metadata).forEach(key => {
                      if (questionWithMetadata.metadata[key] !== undefined && questionWithMetadata.metadata[key] !== null) {
                        allMetadata.metadataFields.add(key);
                      }
                    });
                    
                    parsedQuestions[q.questionNumber] = questionWithMetadata;
                  }
                });
              });
            } catch (error) {
              console.warn(`Failed to parse values for checklist ${checklistName}:`, error);
            }
          });
        }
      });
    }
  });
  
  return { parsedQuestions, allMetadata };
};

// Parse all questions and metadata
const { parsedQuestions, allMetadata } = parseActualQuestions();

console.log('📊 COMPLETE DATA LINKING VERIFICATION:');
console.log(`🏢 Total Frameworks Processed: ${allMetadata.totalFrameworks}`);
console.log(`📂 Total Sections Processed: ${allMetadata.totalSections}`);
console.log(`📄 Total Checklists Processed: ${allMetadata.totalChecklists}`);
console.log(`🔧 Total Components Processed: ${allMetadata.totalComponents}`);
console.log(`❓ Total Questions Linked: ${allMetadata.totalQuestions}`);
console.log(`📋 Questions with Legal Compliance: ${allMetadata.legalComplianceCount}`);
console.log(`🎯 Questions with Scoring: ${allMetadata.scoringQuestions}`);

console.log(`\n🏷️ Categories Found (${allMetadata.categoriesFound.size}):`);
Array.from(allMetadata.categoriesFound).sort().forEach(category => {
  const questionsInCategory = Object.values(parsedQuestions).filter(q => q.category === category).length;
  console.log(`  📌 ${category}: ${questionsInCategory} questions`);
});

console.log(`\n🔤 Question Types Found (${allMetadata.questionTypes.size}):`);
Array.from(allMetadata.questionTypes).sort().forEach(type => {
  const questionsOfType = Object.values(parsedQuestions).filter(q => q.metadata.type === type).length;
  console.log(`  🔹 Type ${type}: ${questionsOfType} questions`);
});

console.log(`\n📝 Required Response Types Found (${allMetadata.requiredResponseTypes.size}):`);
Array.from(allMetadata.requiredResponseTypes).sort().forEach(responseType => {
  const questionsWithResponse = Object.values(parsedQuestions).filter(q => q.metadata.requiredResponse === responseType).length;
  console.log(`  🔸 ${responseType}: ${questionsWithResponse} questions`);
});

console.log(`\n🗂️ Metadata Fields Captured (${allMetadata.metadataFields.size}):`);
Array.from(allMetadata.metadataFields).sort().forEach(field => {
  console.log(`  🔹 ${field}`);
});

console.log(`\n✅ VERIFICATION COMPLETE!`);
console.log(`🎯 ALL ${allMetadata.totalQuestions} questions from actual.json are now linked to the heatmap with complete metadata!`);
console.log(`📈 Total metadata fields captured: ${allMetadata.metadataFields.size}`);
console.log(`🔗 Every question now includes framework, section, checklist, component, and question-level metadata.`);

// Save verification results
const outputPath = path.join(__dirname, '../verification_complete_data_linking.json');
fs.writeFileSync(outputPath, JSON.stringify({
  summary: {
    totalFrameworks: allMetadata.totalFrameworks,
    totalSections: allMetadata.totalSections,
    totalChecklists: allMetadata.totalChecklists,
    totalComponents: allMetadata.totalComponents,
    totalQuestions: allMetadata.totalQuestions,
    legalComplianceCount: allMetadata.legalComplianceCount,
    scoringQuestions: allMetadata.scoringQuestions,
    categoriesFound: Array.from(allMetadata.categoriesFound),
    questionTypes: Array.from(allMetadata.questionTypes),
    requiredResponseTypes: Array.from(allMetadata.requiredResponseTypes),
    metadataFields: Array.from(allMetadata.metadataFields)
  },
  sampleQuestions: Object.entries(parsedQuestions).slice(0, 3).reduce((acc, [key, value]) => {
    acc[key] = value;
    return acc;
  }, {})
}, null, 2));

console.log(`\n💾 Verification results saved to: ${outputPath}`);
