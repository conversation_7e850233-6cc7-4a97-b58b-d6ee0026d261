import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronDown, ChevronRight } from "lucide-react";
import heatmapData from "@/data/heatmapData.json";
import questionsData from "@/data/questionsData.json";
import actualData from "@/data/actual.json";

interface Score {
  period: string;
  value: number;
  status: string;
}

interface Question {
  question: string;
  response: string;
  category: string;
  subcategory: string;
  // Additional metadata from actual.json
  metadata?: {
    id: string;
    type: string;
    questionNumber: string;
    numerator?: number;
    denominator?: number;
    yesScore?: number;
    noScore?: number;
    naScore?: number;
    requiredResponse?: string;
    legalCompliance?: boolean;
    marks?: string;
    // Component metadata
    componentId?: string;
    componentType?: string;
    componentTitle?: string;
    componentDescription?: string;
    componentRequired?: boolean;
    componentOrder?: number;
    // Checklist metadata
    checklistId?: number;
    checklistName?: string;
    checklistCreatedAt?: string | null;
    checklistUpdatedAt?: string | null;
    checklistStatus?: string | null;
    checklistLastModified?: string;
    checklistTotalComponents?: number;
    checklistTotalQuestions?: number;
    // Section metadata
    sectionId?: number;
    sectionName?: string;
    sectionCreatedAt?: string | null;
    sectionUpdatedAt?: string | null;
    sectionStatus?: string | null;
    sectionSupplyCategoryId?: number;
    // Framework metadata
    frameworkId?: number;
    frameworkName?: string;
    frameworkCreatedAt?: string | null;
    frameworkUpdatedAt?: string | null;
    frameworkStatus?: string | null;
    frameworkRemarks?: string | null;
  };
}

interface ActualQuestion {
  id: string;
  text: string;
  type: string;
  questionNumber: string;
  numerator?: number;
  denominator?: number;
  yesScore?: number;
  noScore?: number;
  naScore?: number;
  requiredResponse?: string;
  legalCompliance?: boolean;
  category?: string;
  marks?: string;
}

interface ActualComponent {
  id: string;
  type: string;
  title: string;
  content: string;
  description: string;
  required: boolean;
  order: number;
  questions: ActualQuestion[];
}

interface ActualChecklist {
  id: number;
  name: string;
  createdAt: string | null;
  updatedAt: string | null;
  status: string | null;
  supplyCategoryId: number | null;
  supplySectionId: number;
  createdById: string | null;
  values: string;
}

interface ActualSection {
  id: number;
  name: string;
  createdAt: string | null;
  updatedAt: string | null;
  status: string | null;
  supplyCategoryId: number;
  supplyChecklists: ActualChecklist[];
}

interface ActualFramework {
  id: number;
  name: string;
  createdAt: string | null;
  updatedAt: string | null;
  remarks: string | null;
  status: string | null;
  supplySections: ActualSection[];
}

// Function to map framework names to shorter category names for UI
const mapFrameworkToCategory = (frameworkName: string): string => {
  const frameworkMap: { [key: string]: string } = {
    "SUPPLIER SOCIAL STEWARDSHIP FRAMEWORK": "Social Stewardship",
    "SUPPLIER HEALTH & SAFETY FRAMEWORK": "Health & Safety",
    "LEGAL COMPLIANCES": "Legal Compliance",
    "SUPPLIER GOVERNANCE FRAMEWORK": "Governance",
    "SUPPLIER SUSTAINABILITY AMBASSADORSHIP FRAMEWORK": "Sustainability"
  };

  return frameworkMap[frameworkName] || frameworkName;
};

// Function to parse questions from actual.json - ALL FRAMEWORKS
const parseActualQuestions = () => {
  const parsedQuestions: { [key: string]: Question } = {};

  actualData.forEach((framework) => {
    // Process ALL frameworks, not just environmental
    const frameworkName = framework.name;

    if (framework.supplySections) {
      framework.supplySections.forEach((section) => {
        const sectionName = section.name;

        if (section.supplyChecklists) {
          section.supplyChecklists.forEach((checklist) => {
            const checklistName = checklist.name;

            try {
              const parsedValues = JSON.parse(checklist.values);
              const components = parsedValues.components || [];

              components.forEach((component: any) => {
                const questions = component.questions || [];

                questions.forEach((q: ActualQuestion) => {
                  if (q.questionNumber) {
                    // Map framework names to shorter category names for UI
                    let categoryName = sectionName;

                    // For non-environmental frameworks, use framework name as category
                    if (frameworkName !== "SUPPLIER ENVIRONMENTAL FRAMEWORK") {
                      categoryName = mapFrameworkToCategory(frameworkName);
                    }

                    parsedQuestions[q.questionNumber] = {
                      question: q.text,
                      response: generateMockResponse(q),
                      category: categoryName,
                      subcategory: checklistName,
                      // Capture ALL metadata from actual.json
                      metadata: {
                        // Question metadata
                        id: q.id,
                        type: q.type,
                        questionNumber: q.questionNumber,
                        numerator: q.numerator,
                        denominator: q.denominator,
                        yesScore: q.yesScore,
                        noScore: q.noScore,
                        naScore: q.naScore,
                        requiredResponse: q.requiredResponse,
                        legalCompliance: q.legalCompliance,
                        marks: q.marks,
                        // Component metadata
                        componentId: component.id,
                        componentType: component.type,
                        componentTitle: component.title,
                        componentDescription: component.description,
                        componentRequired: component.required,
                        componentOrder: component.order,
                        // Checklist metadata
                        checklistId: checklist.id,
                        checklistName: checklist.name,
                        checklistCreatedAt: checklist.createdAt,
                        checklistUpdatedAt: checklist.updatedAt,
                        checklistStatus: checklist.status,
                        checklistLastModified: parsedValues.lastModified,
                        checklistTotalComponents: parsedValues.totalComponents,
                        checklistTotalQuestions: parsedValues.totalQuestions,
                        // Section metadata
                        sectionId: section.id,
                        sectionName: section.name,
                        sectionCreatedAt: section.createdAt,
                        sectionUpdatedAt: section.updatedAt,
                        sectionStatus: section.status,
                        sectionSupplyCategoryId: section.supplyCategoryId,
                        // Framework metadata
                        frameworkId: framework.id,
                        frameworkName: framework.name,
                        frameworkCreatedAt: framework.createdAt,
                        frameworkUpdatedAt: framework.updatedAt,
                        frameworkStatus: framework.status,
                        frameworkRemarks: framework.remarks
                      }
                    };
                  }
                });
              });
            } catch (error) {
              console.warn(`Failed to parse values for checklist ${checklistName}:`, error);
            }
          });
        }
      });
    }
  });

  return parsedQuestions;
};

// Function to generate dynamic heatmap structure from actual.json
const generateDynamicHeatmapData = (questions: { [key: string]: Question }) => {
  const categories: { [key: string]: { [key: string]: string[] } } = {};

  // Group questions by category and subcategory
  Object.entries(questions).forEach(([questionNumber, question]) => {
    const { category, subcategory } = question;

    if (!categories[category]) {
      categories[category] = {};
    }

    if (!categories[category][subcategory]) {
      categories[category][subcategory] = [];
    }

    categories[category][subcategory].push(questionNumber);
  });

  // Generate mock scores for each question
  const generateMockScore = (questionNumber: string): number => {
    // Generate consistent scores based on question number for demo
    const hash = questionNumber.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return Math.abs(hash % 100) + 1; // 1-100
  };

  const getStatus = (score: number): string => {
    if (score === 100) return "excellent";
    if (score >= 80) return "good";
    if (score >= 60) return "fair";
    if (score >= 40) return "poor";
    return "critical";
  };

  // Convert to heatmap structure
  const heatmapCategories = Object.entries(categories).map(([categoryName, subcategories]) => ({
    name: categoryName,
    subcategories: Object.entries(subcategories).map(([subcategoryName, questionNumbers]) => ({
      name: subcategoryName,
      scores: questionNumbers.map(questionNumber => {
        const score = generateMockScore(questionNumber);
        return {
          period: questionNumber,
          value: score,
          status: getStatus(score)
        };
      })
    }))
  }));

  return {
    supplierEnvironmentalFramework: {
      title: "Question-wise Score Heat Map",
      scoreUnit: "%",
      legend: {
        "100": { label: "100% green", color: "#22c55e", range: "100%" },
        "80-99": { label: "80-99%", color: "#84cc16", range: "80-99%" },
        "60-79": { label: "60-79%", color: "#eab308", range: "60-79%" },
        "40-59": { label: "40-59%", color: "#f97316", range: "40-59%" },
        "20-39": { label: "20-39%", color: "#ef4444", range: "20-39%" },
        "0-19": { label: "<20%", color: "#dc2626", range: "0-19%" }
      },
      categories: heatmapCategories
    }
  };
};

// Function to generate mock responses based on question type
const generateMockResponse = (question: ActualQuestion): string => {
  const responses = {
    A: ["Yes", "No", "N/A"],
    B: ["Yes", "No"],
    C: ["Excellent", "Good", "Fair", "Poor"],
    D: ["Municipal Water", "Ground Water", "Surface Water", "Recycled Water"]
  };

  const typeResponses = responses[question.type as keyof typeof responses] || ["Yes", "No"];
  return typeResponses[Math.floor(Math.random() * typeResponses.length)];
};

interface ComplianceIssue {
  type: 'critical' | 'performance' | 'documentation' | 'environmental';
  severity: 'high' | 'medium' | 'low';
  description: string;
}

// Compliance issue detection and styling
const getComplianceIssues = (score: Score, question?: Question): ComplianceIssue[] => {
  const issues: ComplianceIssue[] = [];

  // Critical performance issues (scores below 40%)
  if (score.value < 40) {
    issues.push({
      type: 'performance',
      severity: 'high',
      description: `Critical performance score: ${score.value}%`
    });
  }

  if (question) {
    // Critical regulatory violations - "No" responses to critical questions
    const criticalKeywords = ['monitor', 'treatment', 'disposal', 'compliance', 'authorized', 'documented', 'trained'];
    const isCriticalQuestion = criticalKeywords.some(keyword =>
      question.question.toLowerCase().includes(keyword)
    );

    if (question.response.toLowerCase() === 'no' && isCriticalQuestion) {
      issues.push({
        type: 'critical',
        severity: 'high',
        description: 'Critical regulatory non-compliance detected'
      });
    }

    // Documentation gaps
    const documentationKeywords = ['sop', 'documented', 'records', 'maintain', 'procedure'];
    const isDocumentationQuestion = documentationKeywords.some(keyword =>
      question.question.toLowerCase().includes(keyword)
    );

    if (question.response.toLowerCase() === 'no' && isDocumentationQuestion) {
      issues.push({
        type: 'documentation',
        severity: 'medium',
        description: 'Missing documentation or procedures'
      });
    }

    // Environmental risk issues
    const environmentalKeywords = ['hazardous', 'waste', 'discharge', 'pollution', 'contamination'];
    const isEnvironmentalQuestion = environmentalKeywords.some(keyword =>
      question.question.toLowerCase().includes(keyword)
    );

    if (question.response.toLowerCase() === 'no' && isEnvironmentalQuestion) {
      issues.push({
        type: 'environmental',
        severity: 'high',
        description: 'Environmental risk or violation detected'
      });
    }
  }

  return issues;
};

const getComplianceBorderStyle = (issues: ComplianceIssue[]): string => {
  if (issues.length === 0) return '';

  // Prioritize by severity and type
  const highSeverityIssues = issues.filter(issue => issue.severity === 'high');
  const primaryIssue = highSeverityIssues.length > 0 ? highSeverityIssues[0] : issues[0];

  switch (primaryIssue.type) {
    case 'critical':
      return 'border-4 border-red-600 shadow-red-200 shadow-lg';
    case 'performance':
      return 'border-4 border-orange-500 shadow-orange-200 shadow-lg';
    case 'documentation':
      return 'border-4 border-yellow-500 shadow-yellow-200 shadow-lg';
    case 'environmental':
      return 'border-4 border-purple-600 shadow-purple-200 shadow-lg';
    default:
      return '';
  }
};

interface HeatmapCellProps {
  score: Score;
  onClick: (questionId: string, score: Score) => void;
  question?: Question;
}

const HeatmapCell = ({ score, onClick, question }: HeatmapCellProps) => {
  const getColor = (value: number) => {
    if (value === 100) return "bg-green-500";
    if (value >= 80) return "bg-lime-500";
    if (value >= 60) return "bg-yellow-500";
    if (value >= 40) return "bg-orange-500";
    if (value >= 20) return "bg-red-500";
    return "bg-red-600";
  };

  const getTextColor = (value: number) => {
    if (value >= 60) return "text-white";
    return "text-white";
  };

  // Get compliance issues for this cell
  const complianceIssues = getComplianceIssues(score, question);
  const complianceBorderStyle = getComplianceBorderStyle(complianceIssues);
  const hasComplianceIssues = complianceIssues.length > 0;

  return (
    <div className="flex flex-col items-center gap-1">
      <div
        className={`
          h-12 min-w-[4rem] w-auto px-2 flex items-center justify-center rounded-md cursor-pointer
          transition-all duration-200 hover:scale-105 hover:shadow-lg
          ${getColor(score.value)} ${getTextColor(score.value)}
          ${hasComplianceIssues ? complianceBorderStyle : 'border border-white/20'}
          ${hasComplianceIssues ? 'relative' : ''}
        `}
        onClick={() => onClick(score.period, score)}
        title={hasComplianceIssues ? `Compliance Issues: ${complianceIssues.map(i => i.description).join(', ')}` : ''}
      >
        <span className="text-xs font-medium whitespace-nowrap">{score.period}</span>
        {hasComplianceIssues && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-600 rounded-full border-2 border-white flex items-center justify-center">
            <span className="text-white text-xs font-bold">!</span>
          </div>
        )}
      </div>
      <span className="text-xs text-slate-600 font-semibold">{score.value}%</span>
      {hasComplianceIssues && (
        <div className="text-xs text-red-600 font-medium">
          {complianceIssues.length} issue{complianceIssues.length > 1 ? 's' : ''}
        </div>
      )}
    </div>
  );
};

const FilterSection = ({
  selectedSupplierCategory,
  setSelectedSupplierCategory,
  selectedGroup,
  setSelectedGroup,
  selectedNameVendor,
  setSelectedNameVendor,
  selectedLocation,
  setSelectedLocation
}: {
  selectedSupplierCategory: string;
  setSelectedSupplierCategory: (value: string) => void;
  selectedGroup: string;
  setSelectedGroup: (value: string) => void;
  selectedNameVendor: string;
  setSelectedNameVendor: (value: string) => void;
  selectedLocation: string;
  setSelectedLocation: (value: string) => void;
}) => {
  // Mock data for filter options
  const supplierCategories = [
    { value: "all", label: "All Categories" },
    { value: "tier1", label: "Casting & Machining" },
    { value: "tier2", label: "Proprietary Electrical " },
    { value: "tier3", label: "Proprietary Mechanical" },
    { value: "critical", label: "Pressing & fabrication" }
  ];

  const groups = [
    { value: "all", label: "All Groups" },
    { value: "manufacturing", label: "Critical & Stratefic Partners" },
    { value: "logistics", label: "Top 10 supplier groups( May consistent of 100+ vendor codes)" },
    { value: "services", label: "Others" }
  ];

  const nameVendorCodes = [
    { value: "all", label: "All Suppliers" },
    { value: "acme-corp-001", label: "MINDA INDUSTRIES LIMITED - 21687" },
    { value: "global-tech-002", label: "EXIDE INDUSTRIES LIMITED - 20840" },
    { value: "eco-solutions-003", label: "MINDA INDUSTRIES LIMITED - 21683" },
    { value: "green-manufacturing-004", label: "SUPPLIER 1 - 99999" },
    { value: "sustainable-logistics-005", label: "MINDA INDUSTRIES LTD - 20288" }
  ];

  const locations = [
    { value: "all", label: "All Locations" },
    { value: "north-america", label: "Bhawal" },
    { value: "europe", label: "Bawal" },
    { value: "asia-pacific", label: "Chennai" },
    { value: "latin-america", label: "Hosur" },
    { value: "middle-east-africa", label: "Coimbatore" }
  ];

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg">Filters</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700">Supplier Category</label>
            <Select value={selectedSupplierCategory} onValueChange={setSelectedSupplierCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {supplierCategories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700">Group</label>
            <Select value={selectedGroup} onValueChange={setSelectedGroup}>
              <SelectTrigger>
                <SelectValue placeholder="Select group" />
              </SelectTrigger>
              <SelectContent>
                {groups.map((group) => (
                  <SelectItem key={group.value} value={group.value}>
                    {group.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700">Name & Vendor Code</label>
            <Select value={selectedNameVendor} onValueChange={setSelectedNameVendor}>
              <SelectTrigger>
                <SelectValue placeholder="Select supplier" />
              </SelectTrigger>
              <SelectContent>
                {nameVendorCodes.map((supplier) => (
                  <SelectItem key={supplier.value} value={supplier.value}>
                    {supplier.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-slate-700">Location</label>
            <Select value={selectedLocation} onValueChange={setSelectedLocation}>
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location.value} value={location.value}>
                    {location.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const Legend = ({ globalFilter, setGlobalFilter }: { globalFilter: string; setGlobalFilter: (value: string) => void }) => {
  const legendItems = [
    { label: "100% green", color: "bg-green-500", range: "100%" },
    { label: "80-99%", color: "bg-lime-500", range: "80-99%" },
    { label: "60-79%", color: "bg-yellow-500", range: "60-79%" },
    { label: "40-59%", color: "bg-orange-500", range: "40-59%" },
    { label: "20-39%", color: "bg-red-500", range: "20-39%" },
    { label: "<20%", color: "bg-red-600", range: "0-19%" }
  ];

  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">Performance Legend</CardTitle>
        <div className="space-y-2 min-w-[200px] ml-4">
          <label className="text-sm font-medium text-slate-700">Global Filter</label>
          <Select value={globalFilter} onValueChange={setGlobalFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Select filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="compliance">Compliance Issues</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-4">
          {legendItems.map((item, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className={`w-4 h-4 rounded ${item.color}`} />
              <span className="text-sm text-slate-700">{item.label}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const EnvironmentalHeatmap = () => {
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(null);
  const [selectedScore, setSelectedScore] = useState<Score | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Parse questions from actual.json on component initialization
  const actualQuestions = parseActualQuestions();

  // Generate dynamic heatmap data from actual.json instead of using static heatmapData.json
  const dynamicHeatmapData = generateDynamicHeatmapData(actualQuestions);
  const data = dynamicHeatmapData.supplierEnvironmentalFramework;

  // Filter states
  const [selectedSupplierCategory, setSelectedSupplierCategory] = useState<string>("all");
  const [selectedGroup, setSelectedGroup] = useState<string>("all");
  const [selectedNameVendor, setSelectedNameVendor] = useState<string>("all");
  const [selectedLocation, setSelectedLocation] = useState<string>("all");

  // Accordion states for each category - now dynamic based on actual.json categories
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>(
    Object.fromEntries(data.categories.map(cat => [cat.name, false]))
  );

  // Add global filter state
  const [globalFilter, setGlobalFilter] = useState<string>("all");

  const handleCellClick = (questionId: string, score: Score) => {
    // Now primarily use actualQuestions since all 304 questions are parsed from actual.json
    const question = actualQuestions[questionId] || questionsData.questions[questionId as keyof typeof questionsData.questions];
    if (question) {
      setSelectedQuestion(question);
      setSelectedScore(score);
      setIsDialogOpen(true);
    }
  };

  const toggleCategory = (categoryName: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }));
  };

  const getResponseBadgeColor = (response: string) => {
    switch (response.toLowerCase()) {
      case 'yes':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'no':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'n/a':
        return 'bg-gray-100 text-gray-800 border-gray-300';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-300';
    }
  };

  const getPercentageBadgeColor = (value: number) => {
    if (value === 100) return 'bg-green-100 text-green-800 border-green-300';
    if (value >= 80) return 'bg-lime-100 text-lime-800 border-lime-300';
    if (value >= 60) return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    if (value >= 40) return 'bg-orange-100 text-orange-800 border-orange-300';
    if (value >= 20) return 'bg-red-100 text-red-800 border-red-300';
    return 'bg-red-200 text-red-900 border-red-400';
  };

  return (
    <div className="space-y-6">
      <FilterSection
        selectedSupplierCategory={selectedSupplierCategory}
        setSelectedSupplierCategory={setSelectedSupplierCategory}
        selectedGroup={selectedGroup}
        setSelectedGroup={setSelectedGroup}
        selectedNameVendor={selectedNameVendor}
        setSelectedNameVendor={setSelectedNameVendor}
        selectedLocation={selectedLocation}
        setSelectedLocation={setSelectedLocation}
      />

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
        <div className="flex-1">
          <Legend globalFilter={globalFilter} setGlobalFilter={setGlobalFilter} />
        </div>
      </div>

      {/* Question Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Question Details
            </DialogTitle>
          </DialogHeader>
          {selectedQuestion && selectedScore && (() => {
            const complianceIssues = getComplianceIssues(selectedScore, selectedQuestion);
            const hasComplianceIssues = complianceIssues.length > 0;

            return (
              <div className="space-y-4">
                {/* Compliance Issues Alert */}
                {hasComplianceIssues && (
                  <div className="bg-red-50 border-2 border-red-200 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <div className="text-red-600 text-xl">⚠️</div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-red-800 mb-2">
                          Compliance Issues Detected ({complianceIssues.length})
                        </h3>
                        <div className="space-y-2">
                          {complianceIssues.map((issue, index) => (
                            <div key={index} className={`p-3 rounded border-l-4 ${
                              issue.type === 'critical' ? 'bg-red-100 border-red-600' :
                              issue.type === 'performance' ? 'bg-orange-100 border-orange-500' :
                              issue.type === 'documentation' ? 'bg-yellow-100 border-yellow-500' :
                              'bg-purple-100 border-purple-600'
                            }`}>
                              <div className="flex items-center gap-2 mb-1">
                                <span className={`px-2 py-1 text-xs font-semibold rounded ${
                                  issue.severity === 'high' ? 'bg-red-200 text-red-800' :
                                  issue.severity === 'medium' ? 'bg-yellow-200 text-yellow-800' :
                                  'bg-blue-200 text-blue-800'
                                }`}>
                                  {issue.severity.toUpperCase()} PRIORITY
                                </span>
                                <span className="text-sm font-medium text-slate-700">
                                  {issue.type.charAt(0).toUpperCase() + issue.type.slice(1)} Issue
                                </span>
                              </div>
                              <p className="text-sm text-slate-700">{issue.description}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-slate-50 p-4 rounded-lg">
                  <h3 className="font-medium text-slate-700 mb-2">Question:</h3>
                  <p className="text-slate-800">{selectedQuestion.question}</p>
                </div>

                <div className="flex items-center gap-4 flex-wrap">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-slate-700">Response:</span>
                    <Badge
                      variant="outline"
                      className={getResponseBadgeColor(selectedQuestion.response)}
                    >
                      {selectedQuestion.response}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-slate-700">Score:</span>
                    <Badge
                      variant="outline"
                      className={getPercentageBadgeColor(selectedScore.value)}
                    >
                      {selectedScore.value}%
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-slate-700">Category:</span>
                    <p className="text-slate-600">{selectedQuestion.category}</p>
                  </div>
                  <div>
                    <span className="font-medium text-slate-700">Subcategory:</span>
                    <p className="text-slate-600">{selectedQuestion.subcategory}</p>
                  </div>
                </div>
              </div>
            );
          })()}
        </DialogContent>
      </Dialog>
      
      <div className="space-y-8">
        {data.categories.map((category, categoryIndex) => (
          <Card key={categoryIndex} className="shadow-lg">
            <CardHeader
              className="bg-gradient-to-r from-slate-100 to-slate-50 cursor-pointer hover:from-slate-200 hover:to-slate-100 transition-colors"
              onClick={() => toggleCategory(category.name)}
            >
              <CardTitle className="text-2xl text-slate-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {['Water', 'Waste', 'Energy'].includes(category.name) ? (
                      <>
                        <span className="text-lg text-slate-600">Supplier Environmental Framework</span>
                        <span className="text-slate-400">›</span>
                      </>
                    ) : null}
                    <span>{category.name}</span>
                  </div>
                  <div className="flex items-center">
                    {expandedCategories[category.name] ? (
                      <ChevronDown className="h-6 w-6 text-slate-600" />
                    ) : (
                      <ChevronRight className="h-6 w-6 text-slate-600" />
                    )}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            {expandedCategories[category.name] && (
              <CardContent className="pt-6">
                <div className="space-y-6">
                  {category.subcategories.map((subcategory, subIndex) => (
                    <div key={subIndex} className="grid grid-cols-[350px_1fr] gap-6 items-center">
                      <h3 className="text-lg font-semibold text-slate-700 text-left">
                        {subcategory.name}
                      </h3>
                      <div className="flex gap-2 justify-start flex-wrap">
                        {subcategory.scores
                          .filter((score) => {
                            const question = actualQuestions[score.period] || questionsData.questions[score.period as keyof typeof questionsData.questions];
                            if (globalFilter === "compliance") {
                              return getComplianceIssues(score, question).length > 0;
                            }
                            return true;
                          })
                          .map((score, scoreIndex) => {
                            const question = actualQuestions[score.period] || questionsData.questions[score.period as keyof typeof questionsData.questions];
                            return (
                              <HeatmapCell
                                key={scoreIndex}
                                score={score}
                                onClick={handleCellClick}
                                question={question}
                              />
                            );
                          })}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* <Card className="bg-gradient-to-r from-slate-50 to-slate-100">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-slate-700 mb-2">
              Environmental Performance Summary
            </h3>
            <p className="text-slate-600">
              Click on any question number to view the question and response details.
              Colors indicate performance levels from critical (red) to excellent (green).
              <strong className="text-red-700"> Compliance issues are highlighted with colored borders and alert indicators.</strong>
            </p>
          </div>
        </CardContent>
      </Card> */}
    </div>
  );
};
